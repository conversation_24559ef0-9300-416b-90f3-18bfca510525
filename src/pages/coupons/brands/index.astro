---
import MainLayout from '../../../layouts/MainLayout.astro';
import { createServerSupabaseClient } from '../../../utils/supabase/server';
import PageBreadcrumbs from '../../../components/PageBreadcrumbs.astro';
import { GlowingButton } from '../../../components/DesignSystem/Button/GlowingButton';

// Define brand type
interface Brand {
  id: string;
  name: string;
  slug: string;
  logo_url?: string;
  featured?: boolean;
  description?: string;
}

// Fetch all brands with deal counts
const supabase = createServerSupabaseClient({
  cookies: () => Astro.request.headers.get('cookie') || ''
});

// Get all brands first
const { data, error } = await supabase
  .from('brands')
  .select('*')
  .order('name');

if (error) {
  console.error('Error fetching brands:', error);
}

// Process brands and group by first letter
const brands: Brand[] = data || [];

// Group brands by first letter
const groupedBrands: Record<string, Brand[]> = {};
brands.forEach((brand: Brand) => {
  const firstLetter = brand.name.charAt(0).toUpperCase();
  if (!groupedBrands[firstLetter]) {
    groupedBrands[firstLetter] = [];
  }
  groupedBrands[firstLetter].push(brand);
});

// Sort the keys alphabetically
const sortedLetters = groupedBrands ? Object.keys(groupedBrands).sort() : [];

// Enhanced SEO metadata targeting GSC keywords
const title = 'Vape Brand Coupons & Promo Codes - Save Up to 50% | VapeHybrid';
const description = 'Find verified coupon codes for top vape brands like Geek Bar, Lost Mary, Voopoo, and SMOK. Hand-tested promo codes with real success rates. Save up to 50% on premium vape products with exclusive brand discounts updated daily by vapers.';

// Enhanced structured data for better search visibility
const structuredData = {
  '@context': 'https://schema.org',
  '@graph': [
    // Main CollectionPage
    {
      '@type': 'CollectionPage',
      '@id': Astro.url.href,
      name: 'Vape Brand Coupons Directory',
      description: description,
      url: Astro.url.href,
      mainEntity: {
        '@type': 'ItemList',
        numberOfItems: brands.length,
        itemListElement: brands?.map((brand, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'Brand',
            '@id': `${Astro.url.origin}/coupons/brands/${brand.slug}`,
            name: brand.name,
            url: `${Astro.url.origin}/coupons/brands/${brand.slug}`,
            logo: brand.logo_url || undefined,
            description: brand.description || `Save with verified ${brand.name} coupon codes and promo codes`
          }
        }))
      }
    },
    // BreadcrumbList Schema
    {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: Astro.url.origin
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Coupons',
          item: `${Astro.url.origin}/coupons`
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: 'Brand Coupons',
          item: Astro.url.href
        }
      ]
    },
    // FAQ Schema for brand-related questions
    {
      '@type': 'FAQPage',
      mainEntity: [
        {
          '@type': 'Question',
          name: 'Which vape brands offer the best coupon codes?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'Top vape brands like Geek Bar, Lost Mary, Voopoo, and SMOK regularly offer exclusive coupon codes with savings up to 50%. All codes are verified daily by our vaper community.'
          }
        },
        {
          '@type': 'Question',
          name: 'How often are brand coupon codes updated?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'We update brand coupon codes daily and verify each code with real purchases. Expired codes are removed immediately to ensure 98% success rate.'
          }
        },
        {
          '@type': 'Question',
          name: 'Can I stack multiple brand coupon codes?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'Most vape brands allow only one coupon code per order, but some merchants accept manufacturer coupons plus store discounts. Check each brand\'s terms for stacking policies.'
          }
        }
      ]
    }
  ]
};

// Popular brands for featured section
const popularBrands = brands.slice(0, 8);
---

<MainLayout title={title} description={description} structuredData={structuredData}>
  <!-- Hero Section -->
  <div class="relative bg-gradient-to-br from-design-background via-design-background to-design-muted/20 py-16 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-30">
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,_rgba(255,255,255,0.15)_1px,_transparent_0)] bg-[length:20px_20px] dark:bg-[radial-gradient(circle_at_1px_1px,_rgba(255,255,255,0.05)_1px,_transparent_0)]"></div>
    </div>

    <!-- Content -->
    <div class="max-w-[1280px] mx-auto px-4 relative z-10">
      <div class="max-w-3xl mx-auto text-center">
        <PageBreadcrumbs 
          items={[
            { label: 'Home', href: '/' },
            { label: 'Coupons', href: '/coupons' },
            { label: 'Brand Coupons', href: '/coupons/brands', current: true }
          ]}
          class="text-center" 
        />
        <h1 class="text-4xl md:text-5xl font-normal text-design-foreground mb-6 leading-tight">
          Vape Brand Coupons & Promo Codes - Save Big on Your Favorite Gear
        </h1>
        <p class="text-lg text-design-muted-foreground mb-8 max-w-2xl mx-auto">
          Score exclusive deals from top vape brands! Whether you're into Geek Bar disposables, Lost Mary flavors, or Voopoo mods, we've got verified coupon codes that actually work. All deals tested by real vapers, not bots.
        </p>
        
        <!-- Brand Statistics -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 p-6 bg-design-card/50 border border-design-border rounded-lg max-w-2xl mx-auto">
          <div class="text-center">
            <div class="text-2xl font-bold text-primary">{brands.length}</div>
            <div class="text-sm text-design-muted-foreground">Brands</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-primary">500+</div>
            <div class="text-sm text-design-muted-foreground">Active Coupons</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-primary">50%</div>
            <div class="text-sm text-design-muted-foreground">Max Savings</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-primary">98%</div>
            <div class="text-sm text-design-muted-foreground">Success Rate</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-[1280px] mx-auto px-4 pt-4 sm:pt-8 lg:pt-12 pb-12">
    {/* Error Message */}
    {error && (
      <div class="bg-design-destructive/10 text-design-destructive p-4 rounded-md mb-8">
        <p>There was an error loading brand coupons. Please try again later.</p>
      </div>
    )}

    {/* Popular Brands Section */}
    {popularBrands.length > 0 && (
      <div class="mb-12">
        <h2 class="text-2xl font-bold text-design-foreground mb-6">Most Popular Vape Brand Deals</h2>
        <p class="text-design-muted-foreground mb-8">These top vape brands consistently offer the best coupon codes with highest success rates among our vaper community. Updated daily.</p>
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 md:gap-6">
          {popularBrands.map((brand: Brand) => (
            <a
              href={`/coupons/brands/${brand.slug}`}
              class="group bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 hover:border-transparent rounded-xl p-6 transition-all duration-300 hover:shadow-md flex flex-col items-center text-center relative overflow-hidden card-glow"
            >
              {/* Brand image */}
              <div class="w-20 h-20 mb-4 rounded-full bg-primary/5 dark:bg-primary/10 flex items-center justify-center overflow-hidden relative">
                {brand.logo_url ? (
                  <img
                    src={brand.logo_url}
                    alt={`${brand.name} logo`}
                    class="w-12 h-12 object-contain"
                    loading="lazy"
                  />
                ) : (
                  <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                    <span class="text-primary font-bold text-lg">{brand.name.charAt(0)}</span>
                  </div>
                )}
              </div>

              {/* Brand name */}
              <h3 class="font-semibold text-design-foreground group-hover:text-primary transition-colors text-sm">
                {brand.name}
              </h3>
              <p class="text-xs text-design-muted-foreground mt-1">View Coupons</p>
            </a>
          ))}
        </div>
      </div>
    )}

    {/* Alphabetical Index - Enhanced with aria labels for accessibility */}
    <div class="mb-8 p-6 bg-design-card/30 border border-design-border rounded-lg shadow-sm">
      <h3 class="text-base font-semibold text-design-foreground mb-3">Quick Vape Brand Navigation</h3>
      <p class="text-sm text-design-muted-foreground mb-4">Jump directly to your favorite vape brand using the alphabetical shortcuts below:</p>
      <div class="flex flex-wrap gap-2">
        {sortedLetters.map((letter) => (
          <a
            href={`#section-${letter}`}
            class="w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/20 text-primary dark:text-design-secondary font-semibold text-sm flex items-center justify-center hover:bg-primary hover:text-white transition-colors"
            aria-label={`Jump to brands starting with ${letter}`}
          >
            {letter}
          </a>
        ))}
      </div>
    </div>

    {/* Brands Grid - Enhanced for SEO and Responsive Design */}
    <div class="space-y-20">
      {sortedLetters.map((letter) => (
        <div id={`section-${letter}`} class="scroll-mt-24">
          <div class="flex flex-col sm:flex-row items-start sm:items-center mb-8">
            <div class="flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 dark:bg-primary/20 text-primary dark:text-design-secondary font-bold text-2xl mr-4 mb-4 sm:mb-0">
              {letter}
            </div>
            <div>
              <h2 class="text-2xl font-bold text-design-foreground mb-2">Vape Brand Coupons - {letter}</h2>
              <p class="text-sm text-design-muted-foreground">Browse all vape brands starting with '{letter}' and find verified coupon codes from these manufacturers</p>
            </div>
          </div>

          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10">
            {groupedBrands[letter].map((brand: Brand) => (
              <a
                href={`/coupons/brands/${brand.slug}`}
                class="group bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 hover:border-transparent rounded-xl p-8 transition-all duration-300 hover:shadow-md flex flex-col items-center text-center relative overflow-hidden card-glow"
              >
                {/* Brand image with enhanced styling */}
                <div class="w-32 h-32 mb-6 rounded-full bg-primary/5 dark:bg-primary/10 flex items-center justify-center overflow-hidden relative">
                  {brand.logo_url ? (
                    <img
                      src={brand.logo_url}
                      alt={`${brand.name} logo`}
                      class="w-20 h-20 object-contain group-hover:scale-110 transition-transform duration-300"
                      loading="lazy"
                    />
                  ) : (
                    <div class="w-20 h-20 bg-primary/20 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <span class="text-primary font-bold text-2xl">{brand.name.charAt(0)}</span>
                    </div>
                  )}
                  
                  {/* Hover overlay */}
                  <div class="absolute inset-0 bg-primary/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                {/* Brand info */}
                <h3 class="text-xl font-semibold text-design-foreground group-hover:text-primary transition-colors mb-2">
                  {brand.name}
                </h3>
                
                {/* Coupon info */}
                <div class="text-sm text-design-muted-foreground mb-4">
                  <span class="inline-flex items-center px-2 py-1 rounded-full bg-primary/10 text-primary">
                    View Coupon Codes
                  </span>
                </div>

                {brand.description && (
                  <p class="text-sm text-design-muted-foreground text-center line-clamp-2">
                    {brand.description}
                  </p>
                )}

                {/* Hover arrow */}
                <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </div>
              </a>
            ))}
          </div>
        </div>
      ))}
    </div>

    {/* Call to Action */}
    <div class="mt-16 text-center p-8 bg-gradient-to-r from-primary/5 to-design-secondary/5 rounded-xl border border-design-border">
      <h3 class="text-2xl font-bold text-design-foreground mb-4">Can't Find Your Brand?</h3>
      <p class="text-design-muted-foreground mb-6 max-w-2xl mx-auto">
        We're constantly adding new brand partnerships and coupon codes. Browse all available coupons or contact us to suggest a brand.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <GlowingButton client:load asLink={true} href="/coupons" className="text-sm px-6 py-2.5">
          Browse All Coupons
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
        </GlowingButton>
        <a href="/contact" class="inline-flex items-center px-6 py-2.5 border border-design-border rounded-lg text-design-foreground hover:bg-design-muted/10 transition-colors">
          Suggest a Brand
        </a>
      </div>
    </div>
  </div>
</MainLayout>
