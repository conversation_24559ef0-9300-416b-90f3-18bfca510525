---
import MainLayout from '../../../layouts/MainLayout.astro';
import { createServerSupabaseClient } from '../../../utils/supabase/server';
import PageBreadcrumbs from '../../../components/PageBreadcrumbs.astro';
import { GlowingButton } from '../../../components/DesignSystem/Button/GlowingButton';

// Define merchant type
interface Merchant {
  id: string;
  name: string;
  slug?: string;
  logo_url?: string;
  website_url?: string;
  status: string;
}

// Fetch all merchants
const supabase = createServerSupabaseClient({
  cookies: () => Astro.request.headers.get('cookie') || ''
});

const { data, error } = await supabase
  .from('merchants')
  .select('*')
  .eq('status', 'active')
  .order('name');

if (error) {
  console.error('Error fetching merchants:', error);
}

const merchants = data || [];

// Enhanced SEO metadata targeting GSC keywords
const title = 'Vape Store Coupons & Promo Codes - EJuice Connect, VaporDNA & More | VapeHybrid';
const description = 'Get verified coupon codes for top vape stores like EJuice Connect, VaporDNA, EightVape, and Element Vape. Hand-tested promo codes with real success rates from actual vapers. Save up to 50% on e-liquids, devices, and accessories.';

// Enhanced structured data targeting merchant-specific searches
const structuredData = {
  '@context': 'https://schema.org',
  '@graph': [
    // Main CollectionPage
    {
      '@type': 'CollectionPage',
      '@id': Astro.url.href,
      name: 'Vape Store Coupons Directory',
      description: description,
      url: Astro.url.href,
      mainEntity: {
        '@type': 'ItemList',
        numberOfItems: merchants.length,
        itemListElement: data?.map((merchant, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'LocalBusiness',
            '@id': `${Astro.url.origin}/coupons/merchants/${merchant.slug || merchant.id}`,
            name: merchant.name,
            url: `${Astro.url.origin}/coupons/merchants/${merchant.slug || merchant.id}`,
            logo: merchant.logo_url || undefined,
            description: `Save with verified ${merchant.name} coupon codes and promo codes`
          }
        }))
      }
    },
    // BreadcrumbList Schema
    {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: Astro.url.origin
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Coupons',
          item: `${Astro.url.origin}/coupons`
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: 'Store Coupons',
          item: Astro.url.href
        }
      ]
    },
    // FAQ Schema targeting merchant questions
    {
      '@type': 'FAQPage',
      mainEntity: [
        {
          '@type': 'Question',
          name: 'Which vape stores have the best coupon codes?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'Top vape stores like EJuice Connect, VaporDNA, EightVape, and Element Vape regularly offer exclusive coupon codes with savings up to 50%. All codes are verified daily by our community.'
          }
        },
        {
          '@type': 'Question',
          name: 'How do I use vape store coupon codes?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'Copy the coupon code, add items to your cart on the vape store website, and paste the code at checkout. Most stores have a "promo code" or "coupon code" field during payment.'
          }
        },
        {
          '@type': 'Question',
          name: 'Do vape store coupons work on sale items?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'This varies by store. Some vape merchants allow coupon codes on sale items, while others exclude clearance products. Check the coupon terms or contact the store directly.'
          }
        }
      ]
    }
  ]
};
---

<MainLayout title={title} description={description} structuredData={structuredData}>
  <div class="max-w-[960px] mx-auto px-4 pt-4 sm:pt-2 lg:pt-4 pb-8">
    <!-- Breadcrumbs -->
    <PageBreadcrumbs
      items={[
        { label: 'Home', href: '/' },
        { label: 'Coupons', href: '/coupons' },
        { label: 'Merchants', href: '/coupons/merchants', current: true }
      ]}
      class="text-center"
    />

    <!-- Header Section -->
    <div class="mb-12 text-center py-8">
      <h1 class="text-4xl md:text-5xl font-normal text-design-foreground mb-6 leading-tight max-w-4xl mx-auto">
        Vape Store Coupons & Promo Codes - Save at Your Favorite Shops
      </h1>
      <p class="text-lg text-design-muted-foreground max-w-3xl mx-auto mb-8">
        Score deals at top vape shops like EJuice Connect, VaporDNA, EightVape, and Element Vape! We test every coupon code with real purchases so you know they actually work. Save up to 50% on everything from premium e-liquids to the latest devices.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <GlowingButton client:load asLink={true} href="/coupons">
          Browse All Coupons
        </GlowingButton>
        <GlowingButton client:load asLink={true} href="/coupons/brands" variant="secondary">
          Shop by Brand
        </GlowingButton>
      </div>
    </div>

    <!-- Featured Merchants Section -->
    <div class="mt-12 mb-16">
      <h2 class="text-2xl font-bold text-design-foreground mb-6 text-center">Top Vape Shops with Verified Coupon Codes</h2>
      <p class="text-design-muted-foreground mb-8 text-center">These vape retailers consistently offer the best discounts and highest coupon success rates among our community of vapers. All codes hand-verified daily.</p>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {merchants.slice(0, 6).map((merchant) => (
          <a
            href={`/coupons/merchants/${merchant.slug || merchant.id}`}
            class="bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 hover:border-primary rounded-xl p-8 transition-all duration-300 hover:shadow-md flex flex-col h-full relative overflow-hidden card-glow"
            aria-label={`Browse ${merchant.name} coupon codes`}
          >
            <div class="flex items-center mb-6">
              {merchant.logo ? (
                <img
                  src={merchant.logo_url}
                  alt={`${merchant.name} logo`}
                  class="w-16 h-16 rounded-md mr-4 object-contain"
                  loading="lazy"
                />
              ) : (
                <div class="w-16 h-16 bg-primary/20 rounded-md mr-4 flex items-center justify-center">
                  <span class="text-primary font-bold text-lg">{merchant.name.charAt(0)}</span>
                </div>
              )}
              <h2 class="text-xl font-semibold text-design-foreground group-hover:text-primary transition-colors">{merchant.name}</h2>
            </div>

            <p class="text-design-muted-foreground text-sm mb-6 flex-grow">
              {merchant.short_description || `Exclusive vape discounts and promo codes for ${merchant.name}`}
            </p>

            {/* Coupon stats */}
            <div class="flex items-center gap-4 text-xs text-design-muted-foreground mb-4">
              <span class="flex items-center gap-1">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                Verified Daily
              </span>
              <span class="flex items-center gap-1">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                </svg>
                Active Codes
              </span>
            </div>

            {/* Call to action */}
            <div class="mt-auto">
              <span class="inline-flex items-center px-4 py-2 bg-design-primary/10 text-design-primary rounded-full text-sm font-medium group-hover:bg-design-primary group-hover:text-white transition-all">
                View Coupons
                <svg class="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                </svg>
              </span>
            </div>
          </a>
        ))}
      </div>
    </div>

    <!-- SEO Content Section -->
    <div class="bg-design-card border border-design-border rounded-xl p-8 mb-12">
      <h2 class="text-2xl font-bold text-design-foreground mb-6 text-center">
        Why Choose VapeHybrid for Merchant Coupons?
      </h2>
      <div class="grid md:grid-cols-2 gap-8">
        <div>
          <h3 class="text-lg font-semibold text-design-foreground mb-3">
            Verified Daily
          </h3>
          <p class="text-design-muted-foreground mb-4">
            Our team verifies every coupon code daily to ensure they work. We test codes across all major vape merchants and remove expired offers immediately.
          </p>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-design-foreground mb-3">
            Exclusive Deals
          </h3>
          <p class="text-design-muted-foreground mb-4">
            Access exclusive coupon codes and promo codes not available elsewhere. We partner directly with vape merchants to bring you the best savings.
          </p>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-design-foreground mb-3">
            Top Vape Stores
          </h3>
          <p class="text-design-muted-foreground mb-4">
            Find coupons for all major vape merchants including EJuice Connect, VaporDNA, EightVape, Element Vape, and many more trusted stores.
          </p>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-design-foreground mb-3">
            Real-Time Updates
          </h3>
          <p class="text-design-muted-foreground mb-4">
            Our system automatically tracks coupon success rates and expiration dates, ensuring you always get working codes with the best savings.
          </p>
        </div>
      </div>
    </div>

    <!-- Popular Merchant Categories -->
    <div class="text-center">
      <h2 class="text-2xl font-bold text-design-foreground mb-8">
        Popular Vape Store Categories
      </h2>
      <div class="flex flex-wrap justify-center gap-4">
        <a href="/coupons/categories/e-liquids" class="px-6 py-3 bg-design-muted/20 hover:bg-design-primary/10 text-design-foreground hover:text-design-primary rounded-full transition-all">
          E-Liquid Coupons
        </a>
        <a href="/coupons/categories/devices" class="px-6 py-3 bg-design-muted/20 hover:bg-design-primary/10 text-design-foreground hover:text-design-primary rounded-full transition-all">
          Device Coupons
        </a>
        <a href="/coupons/categories/accessories" class="px-6 py-3 bg-design-muted/20 hover:bg-design-primary/10 text-design-foreground hover:text-design-primary rounded-full transition-all">
          Accessory Coupons
        </a>
        <a href="/coupons/categories/disposables" class="px-6 py-3 bg-design-muted/20 hover:bg-design-primary/10 text-design-foreground hover:text-design-primary rounded-full transition-all">
          Disposable Coupons
        </a>
      </div>
    </div>
  </div>
</MainLayout>
